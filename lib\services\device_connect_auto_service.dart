import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:esp_blufi/esp_blufi.dart';
import 'package:wifi_scan/wifi_scan.dart';

import '../models/device.dart';
import '../models/wifi.dart';
import '../services/wifi_scan_service.dart';
import '../services/vibration_service.dart';
import '../services/device_service.dart';
import '../utils/app_logger.dart';
import '../constants/constants.dart';

/// 自动配网状态枚举
enum AutoConnectState {
  idle, // 空闲状态
  detecting, // 检测状态
  hotspotConfig, // 热点配网状态
  homeWifiConfig, // 家庭WiFi配网状态
}

/// 自动配网服务
/// 负责监听设备状态变化，自动进行热点配网和家庭WiFi配网
class DeviceConnectAutoService {
  final EspBlufi _espBlufiPlugin = EspBlufi();
  final WifiScanService _wifiScanService = WifiScanService();
  final VibrationService _vibrationService = VibrationService();
  DeviceApiService? _deviceApiService;

  // 状态变量
  AutoConnectState _currentState = AutoConnectState.idle;
  Timer? _homeWifiMonitorTimer;
  bool _isProcessing = false;
  bool _isHostspotEnabled = false;
  bool _isDeviceBle = false;
  bool _isScanStopped = false;
  String? _deviceBleMac;
  String? _deviceName;
  wifiConfig? _wifiComfig;

  // 回调函数
  Function(bool success, String message)? _onConfigComplete;

  // 获取当前状态
  AutoConnectState get currentState => _currentState;

  /// 构造函数
  DeviceConnectAutoService() {
    // 初始化BluFi回调
    _espBlufiPlugin.onMessageReceived(
      successCallback: _handleBlufiCallback,
      errorCallback: (error) {
        AppLogger.error("BluFi错误: $error", error: Exception(error));
      },
    );
  }

  /// 设置配网完成回调
  void setConfigCompleteCallback(
      Function(bool success, String message) callback) {
    _onConfigComplete = callback;
  }

  /// 开始监听设备状态
  void startMonitoring(Device device) {
    if (_isProcessing) return;
    _isProcessing = true;
    _deviceBleMac = device.bleMac;
    _deviceName = device.deviceName;
    _currentState = AutoConnectState.detecting;
    AppLogger.info('开始监听设备状态: ${device.deviceName}');
    _handleDeviceOffline();
  }

  /// 停止监听
  void stopMonitoring() {
    // _cancelTimers();
    _currentState = AutoConnectState.idle;
    _isProcessing = false;
    AppLogger.info('停止监听设备状态');
  }

  /// 处理设备离线事件
  Future<void> _handleDeviceOffline() async {
    _wifiComfig = await wifiConfig.getConfig();
    // 触发设备振动
    await _vibrationService.vibrate();
    // 显示场景选择对话框
    _showSceneSelectionDialog();
    await _vibrationService.stopVibration();
  }

  /// 选择外出遛狗场景
  void selectOutdoorWalkingScene() {
    if (_currentState != AutoConnectState.detecting) return;

    
    //开始自动连接热点
    _startHotspotConfigProcess();
  }

  /// 选择宠物走丢场景
  void selectPetLostScene() {
    if (_currentState != AutoConnectState.detecting) return;
    // 触发走丢提醒功能（这里只是重置状态，实际走丢提醒由其他组件处理）
    _isProcessing = false;
    _currentState = AutoConnectState.idle;
    _updateStatus('已触发走丢提醒功能');
  }

  /// 开始热点配网流程
  Future<void> _startHotspotConfigProcess() async {
    AppLogger.info('开始热点配网流程');

    _currentState = AutoConnectState.hotspotConfig;

    int checkCount = 0;
    bool flag = false;
    while (checkCount < 3) {
      checkCount++;

      // 获取热点配置
      if (_wifiComfig != null && !flag) {
        // 开始蓝牙配网
        flag = true;
        await _startBleConfig(
            _wifiComfig!.hotspotSsid, _wifiComfig!.hotspotPassword);
        flag = false;
      }

      if (_isHostspotEnabled) {
        break;
      }
    }
    //仍未检测到热点则退出
    _updateStatus('未检测到热点，请开启手机热点后重试');
    _onConfigComplete?.call(false, '未检测到热点');
    _resetState();
  }

  /// 开始蓝牙配网
  Future<void> _startBleConfig(String ssid, String password) async {
    if (_deviceBleMac == null) {
      _updateStatus('设备MAC地址为空，无法配网');
      _onConfigComplete?.call(false, '设备MAC地址为空');
      return;
    }

    _updateStatus('开始蓝牙配网，设备连接热点: $_deviceBleMac');

    try {
      // 重置扫描相关状态
      _isDeviceBle = false;
      _isScanStopped = false;

      await _espBlufiPlugin.scanDeviceInfo(filterString: 'PETCARE');

      await Future.delayed(Duration(seconds: 5));

      // 使用超时机制防止stopScan阻塞
      try {
        await _espBlufiPlugin.stopScan().timeout(
          Duration(seconds: 1),
          onTimeout: () {
            AppLogger.warning('stopScan超时，继续执行后续流程');
          },
        );
      } catch (e) {
        AppLogger.warning('stopScan执行失败: $e，继续执行后续流程');
      }

      if (!_isDeviceBle) {
        _updateStatus('未检测到设备蓝牙，请检查设备是否在范围内');
        _onConfigComplete?.call(false, '未检测到设备蓝牙');
        return;
      }

      // 连接设备蓝牙
      await _espBlufiPlugin.connectPeripheral(
          peripheralAddress: _deviceBleMac!);

      // 等待蓝牙连接成功
      await Future.delayed(const Duration(seconds: 2));

      //激活设备WiFi并获取WiFi状态
      await _espBlufiPlugin.requestDeviceStatus().timeout(
        Duration(seconds: 1),
        onTimeout: () {
          AppLogger.warning('requestDeviceStatus超时，继续执行后续流程');
        },
      );
      // 配置WiFi
      _updateStatus('正在配置WiFi: $ssid');
      await _espBlufiPlugin
          .configProvision(
            username: ssid,
            password: password,
          )
          .timeout(Duration(seconds: 1));
    } catch (e, stackTrace) {
      AppLogger.error('蓝牙配网失败: $e', error: e, stackTrace: stackTrace);
      _onConfigComplete?.call(false, '蓝牙配网失败');

      //出现异常后如果蓝牙没有断开则断开蓝牙
      if (!_isScanStopped) {
        await _espBlufiPlugin.stopScan().timeout(Duration(seconds: 1));
      }
    }
  }

  /// 启动家庭WiFi监测
  void _startHomeWifiMonitoring() {
    AppLogger.info('开始监测家庭WiFi信号');

    // 切换到家庭WiFi配网状态
    _currentState = AutoConnectState.homeWifiConfig;
    // 取消之前的定时器
    _homeWifiMonitorTimer?.cancel();

    // 每30秒检测一次家庭WiFi信号
    _homeWifiMonitorTimer = Timer.periodic(
      const Duration(seconds: 30),
      (timer) async {
        try {
          // 获取家庭WiFi配置
          if (_wifiComfig == null) {
            AppLogger.warning('未找到保存的家庭WiFi配置');
            return;
          }

          // 扫描WiFi网络
          final accessPoints = await _wifiScanService.scanWifiNetworks();

          // 查找家庭WiFi
          WiFiAccessPoint? homeWifi;
          try {
            homeWifi = accessPoints.firstWhere(
              (ap) => ap.ssid == _wifiComfig!.homeSsid,
            );
          } catch (e) {
            // 未找到家庭WiFi
            homeWifi = null;
          }

          // 检查信号强度是否足够
          if (homeWifi != null &&
              homeWifi.ssid.isNotEmpty &&
              homeWifi.level > -60) {
            AppLogger.info('检测到家庭WiFi信号强度足够: ${homeWifi.level} dBm');

            // 等待5秒后再次确认信号强度
            await Future.delayed(const Duration(seconds: 5));

            final accessPointsConfirm =
                await _wifiScanService.scanWifiNetworks();
            WiFiAccessPoint? homeWifiConfirm;
            try {
              homeWifiConfirm = accessPointsConfirm.firstWhere(
                (ap) => ap.ssid == _wifiComfig!.homeSsid,
              );
            } catch (e) {
              homeWifiConfirm = null;
            }

            // 再次确认信号强度
            if (homeWifiConfirm != null &&
                homeWifiConfirm.ssid.isNotEmpty &&
                homeWifiConfirm.level > -60) {
              timer.cancel();
              _updateStatus('家庭WiFi信号稳定，准备切换到家庭WiFi');

              _startHomeWifiConfigProcess(
                  _wifiComfig!.homeSsid, _wifiComfig!.homePassword);
            }
          }
        } catch (e, stackTrace) {
          AppLogger.error('监测家庭WiFi信号失败: $e', error: e, stackTrace: stackTrace);
        }
      },
    );
  }

  /// 开始家庭WiFi配网流程
  Future<void> _startHomeWifiConfigProcess(String ssid, String password) async {
    AppLogger.info('开始家庭WiFi配网流程');

    try {
      // 发送WIFI_UPDATE指令断开当前WiFi连接

      final success =
          await _deviceApiService!.deviceWifiConfigUpdate(_deviceName!);
      if (!success) {
        _updateStatus('发送WiFi更新指令失败');
        _onConfigComplete?.call(false, '发送WiFi更新指令失败');
        return;
      }

      // 等待设备断开WiFi连接
      await Future.delayed(const Duration(seconds: 2));

      // 开始蓝牙配网
      _startBleConfig(ssid, password);
    } catch (e, stackTrace) {
      AppLogger.error('家庭WiFi配网失败: $e', error: e, stackTrace: stackTrace);
      _onConfigComplete?.call(false, '家庭WiFi配网失败');
    }
  }

  ///检查热点是否存在
  Future<bool> HotspotEnabled() async {
    await _espBlufiPlugin.requestDeviceWifiScan().timeout(Duration(seconds: 5));
    if (_isHostspotEnabled) {
      return true;
    }
    return false;
  }

  /// 处理BluFi回调
  void _handleBlufiCallback(String? data) {
    if (data == null) return;

    try {
      AppLogger.debug("BluFi回调信息: $data");
      Map<String, dynamic> mapData = jsonDecode(data);

      if (mapData.containsKey('key')) {
        String key = mapData['key'];

        if (key == 'ble_scan_result') {
          // 蓝牙扫描结果,检查是否是目标设备
          Map<String, dynamic> peripheral = mapData['value'];
          String address = peripheral['address'];
          String name = peripheral['name'];
          if (name == 'PETCARE' && address == _deviceBleMac) {
            _isDeviceBle = true;
          }
        } else if (key == 'stop_scan_ble') {
          // 扫描停止事件
          _isScanStopped = true;
          AppLogger.debug('蓝牙扫描已停止');
        }
        if (key == 'wifi_info') {
          //设备自身扫描到的WiFi列表
          Map<String, dynamic> peripheral = mapData['value'];
          String ssid = peripheral['ssid'];
          if (ssid == _wifiComfig!.hotspotSsid) {
            _isHostspotEnabled = true;
          }
        }
        if (key == 'GATT_SUCCESS') {
          // BLE连接成功
          _updateStatus('蓝牙连接成功');
        } else if (key == 'disconnected_device') {
          // BLE连接断开 WiFi连接成功
          int wifiConnectResult = mapData['value'];

          if (wifiConnectResult == 1) {
            // WiFi连接成功
            _updateStatus('WiFi连接成功');
            _onConfigComplete?.call(true, 'WiFi配网成功');

            if (_currentState == AutoConnectState.homeWifiConfig) {
              //结束自动配网
              _resetState();
            } else if (_currentState == AutoConnectState.hotspotConfig) {
              // 热点配网结束进入到家庭WIFI配网
              _startHomeWifiMonitoring();
            }
          }
        } else if (key == 'receive_device_custom_data') {
          // 设备自定义数据
          _deviceName = mapData['value'];
          _updateStatus('获取设备名称: $_deviceName');
        }
      }
    } catch (e, stackTrace) {
      AppLogger.error('处理BluFi回调失败: $e', error: e, stackTrace: stackTrace);
    }
  }

  /// 显示场景选择对话框
  void _showSceneSelectionDialog() {
    final globalContext = navigatorKey.currentContext;
    if (globalContext == null) return;
    showDialog(
      context: globalContext,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('项圈已离线'),
          content: const Text(
              '检测到项圈WiFi断开,请确认您的宠物状态:\n 若不是外出遛狗，您的宠物可能已经出逃。若是正在遛狗，点击连接热点按钮，项圈将尝试连接手机热点获得网络，请确认手机热点已开启。'),
          actions: <Widget>[
            TextButton(
              child: const Text('连接热点'),
              onPressed: () {
                Navigator.of(context).pop();
                selectOutdoorWalkingScene();
              },
            ),
            TextButton(
              child: const Text('知道了'),
              onPressed: () {
                Navigator.of(context).pop();
                selectPetLostScene();
              },
            ),
          ],
        );
      },
    );
  }

  /// 更新状态并通知回调
  void _updateStatus(String message) {
    AppLogger.info(message);
  }

  /// 重置状态
  void _resetState() {
    // _cancelTimers();
    _currentState = AutoConnectState.idle;
    _isProcessing = false;
  }

  /// 取消所有定时器
  void _cancelTimers() {
    _homeWifiMonitorTimer?.cancel();
  }

  // 释放资源
  void dispose() {
    _cancelTimers();
  }
}
